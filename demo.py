"""
Demonstração da aplicação PDF Splitter.
Mostra as funcionalidades principais sem interface gráfica.
"""

import os
import tempfile
from pathlib import Path
from pdf_utils import PDFSplitter, validate_pdf_file, validate_target_size
from error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, get_user_friendly_error_message


def create_sample_pdf():
    """Cria um PDF de exemplo para demonstração."""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # Criar arquivo temporário
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.close()
        
        # Criar PDF com múltiplas páginas
        c = canvas.Canvas(temp_file.name, pagesize=letter)
        width, height = letter
        
        for page_num in range(1, 21):  # 20 páginas
            c.setFont("Helvetica-Bold", 16)
            c.drawString(50, height - 50, f"PDF Splitter Demo - Página {page_num}")
            
            c.setFont("Helvetica", 12)
            y_position = height - 100
            
            # Adicionar conteúdo de texto
            for line_num in range(1, 25):
                text = f"Esta é a linha {line_num} da página {page_num}. " * 2
                c.drawString(50, y_position, text[:70])
                y_position -= 20
                
                if y_position < 50:
                    break
            
            c.showPage()
        
        c.save()
        return temp_file.name
        
    except ImportError:
        print("⚠️ reportlab não está instalado. Usando PDF de exemplo fictício.")
        return None
    except Exception as e:
        print(f"❌ Erro ao criar PDF de exemplo: {e}")
        return None


def demo_validation():
    """Demonstra as funções de validação."""
    print("🧪 Demonstração das Funções de Validação")
    print("=" * 50)
    
    # Teste de validação de tamanho
    print("\n📏 Validação de Tamanho:")
    test_sizes = ["5", "10.5", "0.1", "0", "-1", "abc", ""]
    
    for size in test_sizes:
        is_valid, msg, value = validate_target_size(size)
        status = "✅" if is_valid else "❌"
        print(f"  {status} '{size}' -> {msg if not is_valid else f'{value} MB'}")
    
    # Teste de validação de arquivo
    print("\n📄 Validação de Arquivo:")
    test_files = ["arquivo_inexistente.pdf", "demo.py", ""]
    
    for file_path in test_files:
        is_valid, msg = validate_pdf_file(file_path)
        status = "✅" if is_valid else "❌"
        print(f"  {status} '{file_path}' -> {msg}")


def demo_error_handling():
    """Demonstra o sistema de tratamento de erros."""
    print("\n🛡️ Demonstração do Tratamento de Erros")
    print("=" * 50)
    
    # Criar reporter de erros
    reporter = ErrorReporter()
    
    # Adicionar diferentes tipos de erros
    reporter.add_error("Arquivo não encontrado")
    reporter.add_error("Permissão negada")
    reporter.add_warning("Tamanho do arquivo muito grande")
    reporter.add_warning("PDF pode estar corrompido")
    
    print("\n📊 Relatório de Erros:")
    print(reporter.get_summary())
    
    # Demonstrar mensagens amigáveis
    print("\n💬 Mensagens Amigáveis para Usuário:")
    test_errors = [
        Exception("permission denied"),
        Exception("no space left on device"),
        Exception("corrupted file"),
        Exception("network timeout")
    ]
    
    for error in test_errors:
        friendly_msg = get_user_friendly_error_message(error)
        print(f"  • {str(error)} -> {friendly_msg}")


def demo_pdf_analysis(pdf_path):
    """Demonstra a análise de PDF."""
    if not pdf_path or not os.path.exists(pdf_path):
        print("⚠️ PDF de exemplo não disponível para análise.")
        return
    
    print("\n📊 Demonstração da Análise de PDF")
    print("=" * 50)
    
    try:
        splitter = PDFSplitter(pdf_path)
        
        print(f"\n📄 Arquivo: {os.path.basename(pdf_path)}")
        print(f"📃 Total de páginas: {splitter.get_total_pages()}")
        print(f"💾 Tamanho do arquivo: {splitter.get_file_size_mb():.2f} MB")
        
        # Testar diferentes tamanhos alvo
        target_sizes = [2, 5, 10]
        
        for target_size in target_sizes:
            info = splitter.get_split_info(target_size)
            print(f"\n🎯 Para tamanho alvo de {target_size} MB:")
            print(f"  📦 Partes estimadas: {info['estimated_parts']}")
            print(f"  📃 Páginas por parte: ~{info['pages_per_part']}")
            
            # Mostrar intervalos de páginas
            print("  📋 Intervalos de páginas:")
            for i, (start, end) in enumerate(info['ranges'], 1):
                print(f"    Parte {i}: páginas {start + 1}-{end}")
    
    except Exception as e:
        print(f"❌ Erro na análise: {get_user_friendly_error_message(e)}")


def demo_splitting(pdf_path):
    """Demonstra a divisão de PDF."""
    if not pdf_path or not os.path.exists(pdf_path):
        print("⚠️ PDF de exemplo não disponível para divisão.")
        return
    
    print("\n✂️ Demonstração da Divisão de PDF")
    print("=" * 50)
    
    # Criar diretório de saída temporário
    output_dir = tempfile.mkdtemp(prefix="pdf_split_demo_")
    print(f"\n📁 Diretório de saída: {output_dir}")
    
    try:
        splitter = PDFSplitter(pdf_path)
        target_size = 3.0  # 3 MB por parte
        
        print(f"\n🚀 Dividindo PDF em partes de {target_size} MB...")
        
        def progress_callback(current, total, message):
            print(f"  📊 Progresso: {current}/{total} - {message}")
        
        created_files = splitter.split_pdf(output_dir, target_size, progress_callback)
        
        print(f"\n✅ Divisão concluída! Criados {len(created_files)} arquivos:")
        
        total_size = 0
        for file_path in created_files:
            if os.path.exists(file_path):
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                total_size += size_mb
                print(f"  📄 {os.path.basename(file_path)}: {size_mb:.2f} MB")
        
        print(f"\n📊 Tamanho total da saída: {total_size:.2f} MB")
        
        # Limpeza
        print(f"\n🧹 Limpando arquivos temporários...")
        for file_path in created_files:
            try:
                os.remove(file_path)
            except:
                pass
        
        try:
            os.rmdir(output_dir)
        except:
            pass
        
        print("✅ Limpeza concluída!")
    
    except Exception as e:
        print(f"❌ Erro na divisão: {get_user_friendly_error_message(e)}")


def main():
    """Função principal da demonstração."""
    print("🎬 PDF Splitter - Demonstração Completa")
    print("=" * 60)
    
    # Criar PDF de exemplo
    print("\n📝 Criando PDF de exemplo...")
    sample_pdf = create_sample_pdf()
    
    if sample_pdf:
        size_mb = os.path.getsize(sample_pdf) / (1024 * 1024)
        print(f"✅ PDF criado: {os.path.basename(sample_pdf)} ({size_mb:.2f} MB)")
    
    # Executar demonstrações
    demo_validation()
    demo_error_handling()
    demo_pdf_analysis(sample_pdf)
    demo_splitting(sample_pdf)
    
    # Limpeza final
    if sample_pdf and os.path.exists(sample_pdf):
        try:
            os.remove(sample_pdf)
            print("\n🧹 PDF de exemplo removido.")
        except:
            pass
    
    print("\n🎉 Demonstração concluída!")
    print("\nPara usar a aplicação com interface gráfica:")
    print("  python main.py")
    print("\nPara executar testes:")
    print("  python quick_test.py")


if __name__ == "__main__":
    main()
