# PDF Splitter Application

A modern desktop application built with Python and customTkinter that splits large PDF files into smaller parts based on a target file size.

## Features

- 🎨 Modern, user-friendly interface using customTkinter
- 📄 Split large PDF files into smaller parts
- 🎯 Specify target size for each split part (in MB)
- 📊 Real-time progress tracking
- 💾 Choose custom output directory
- ⚡ Efficient processing without loading entire file into memory
- 🛡️ Comprehensive error handling

## Requirements

- Python 3.8 or higher
- Windows, macOS, or Linux

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

1. Run the application:

```bash
python main.py
```

2. Click "Browse" to select a PDF file to split
3. Enter the target size per part (in MB)
4. Choose an output folder where the split files will be saved
5. Click "Split PDF" to start the process
6. Monitor the progress in the status area

## How It Works

The application analyzes the PDF file and calculates how many pages should be included in each part to approximate the target file size. It then creates multiple PDF files, each containing a subset of pages from the original document.

**Note:** The actual size of each part may vary slightly from the target size due to differences in page content (images, text density, etc.).

## Output Files

Split files are named using the pattern:
```
original_filename_part1.pdf
original_filename_part2.pdf
original_filename_part3.pdf
...
```

## Error Handling

The application handles various error scenarios:
- Invalid or corrupted PDF files
- Insufficient disk space
- File permission issues
- Invalid input values
- Missing files or directories

## Project Structure

```
pdf_split/
├── main.py              # Main application entry point
├── pdf_utils.py         # PDF processing utilities
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## Dependencies

- **customtkinter**: Modern UI framework for Python
- **PyPDF2**: PDF manipulation library
- **Pillow**: Image processing (required by customtkinter)

## License

This project is open source and available for personal and commercial use.

## Troubleshooting

### Application won't start
- Ensure Python 3.8+ is installed
- Verify all dependencies are installed: `pip install -r requirements.txt`

### PDF splitting fails
- Check that the PDF file is not corrupted
- Ensure you have write permissions to the output directory
- Verify sufficient disk space is available

### Split files are larger/smaller than expected
- PDF compression varies by content
- Pages with many images will be larger
- Text-heavy pages will be smaller
- The application aims for approximate target sizes

## Contributing

Contributions are welcome! Feel free to submit issues or pull requests.

