"""
PDF Utility Module
Handles PDF analysis, size calculation, and splitting operations.
"""

import os
from pathlib import Path
from typing import List, Tuple, Callable
from PyPDF2 import PdfReader, PdfWriter


class PDFSplitter:
    """Handles PDF splitting operations."""
    
    def __init__(self, pdf_path: str):
        """
        Initialize the PDF splitter.
        
        Args:
            pdf_path: Path to the PDF file to split
            
        Raises:
            FileNotFoundError: If the PDF file doesn't exist
            ValueError: If the file is not a valid PDF
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        self.pdf_path = pdf_path
        self.reader = None
        self._load_pdf()
    
    def _load_pdf(self):
        """Load and validate the PDF file."""
        try:
            self.reader = PdfReader(self.pdf_path)
            # Try to access pages to validate the PDF
            _ = len(self.reader.pages)
        except Exception as e:
            raise ValueError(f"Invalid or corrupted PDF file: {str(e)}")
    
    def get_total_pages(self) -> int:
        """Get the total number of pages in the PDF."""
        return len(self.reader.pages)
    
    def get_file_size_mb(self) -> float:
        """Get the file size in megabytes."""
        size_bytes = os.path.getsize(self.pdf_path)
        return size_bytes / (1024 * 1024)
    
    def estimate_pages_per_part(self, target_size_mb: float) -> int:
        """
        Estimate how many pages should be in each part to approximate target size.
        
        Args:
            target_size_mb: Target size for each part in MB
            
        Returns:
            Estimated number of pages per part
        """
        total_pages = self.get_total_pages()
        total_size_mb = self.get_file_size_mb()
        
        if total_size_mb <= target_size_mb:
            return total_pages
        
        # Calculate average size per page
        avg_page_size_mb = total_size_mb / total_pages
        
        # Estimate pages per part (with a small buffer for PDF overhead)
        pages_per_part = int((target_size_mb * 0.95) / avg_page_size_mb)
        
        # Ensure at least 1 page per part
        return max(1, pages_per_part)
    
    def calculate_split_ranges(self, target_size_mb: float) -> List[Tuple[int, int]]:
        """
        Calculate page ranges for splitting the PDF.
        
        Args:
            target_size_mb: Target size for each part in MB
            
        Returns:
            List of tuples (start_page, end_page) for each part
        """
        total_pages = self.get_total_pages()
        pages_per_part = self.estimate_pages_per_part(target_size_mb)
        
        ranges = []
        start_page = 0
        
        while start_page < total_pages:
            end_page = min(start_page + pages_per_part, total_pages)
            ranges.append((start_page, end_page))
            start_page = end_page
        
        return ranges
    
    def split_pdf(
        self,
        output_dir: str,
        target_size_mb: float,
        progress_callback: Callable[[int, int, str], None] = None
    ) -> List[str]:
        """
        Split the PDF into multiple parts.
        
        Args:
            output_dir: Directory to save the split PDF files
            target_size_mb: Target size for each part in MB
            progress_callback: Optional callback function(current, total, message)
            
        Returns:
            List of paths to the created PDF files
            
        Raises:
            ValueError: If target size is invalid
            OSError: If there are file system issues
        """
        if target_size_mb <= 0:
            raise ValueError("Target size must be greater than 0 MB")
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Get the base filename without extension
        base_name = Path(self.pdf_path).stem
        
        # Calculate split ranges
        ranges = self.calculate_split_ranges(target_size_mb)
        total_parts = len(ranges)
        
        if progress_callback:
            progress_callback(0, total_parts, "Starting PDF split...")
        
        created_files = []
        
        for part_num, (start_page, end_page) in enumerate(ranges, 1):
            # Create a new PDF writer for this part
            writer = PdfWriter()
            
            # Add pages to this part
            for page_num in range(start_page, end_page):
                writer.add_page(self.reader.pages[page_num])
            
            # Generate output filename
            output_filename = f"{base_name}_part{part_num}.pdf"
            output_path = os.path.join(output_dir, output_filename)
            
            # Write the PDF file
            try:
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)
                
                created_files.append(output_path)
                
                # Get the size of the created file
                file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
                
                if progress_callback:
                    message = (f"Created part {part_num}/{total_parts}: "
                             f"{output_filename} ({file_size_mb:.2f} MB, "
                             f"pages {start_page + 1}-{end_page})")
                    progress_callback(part_num, total_parts, message)
                    
            except Exception as e:
                # Clean up any created files on error
                for created_file in created_files:
                    try:
                        os.remove(created_file)
                    except:
                        pass
                raise OSError(f"Failed to write PDF part {part_num}: {str(e)}")
        
        return created_files
    
    def get_split_info(self, target_size_mb: float) -> dict:
        """
        Get information about how the PDF will be split.
        
        Args:
            target_size_mb: Target size for each part in MB
            
        Returns:
            Dictionary with split information
        """
        ranges = self.calculate_split_ranges(target_size_mb)
        total_pages = self.get_total_pages()
        total_size_mb = self.get_file_size_mb()
        
        return {
            'total_pages': total_pages,
            'total_size_mb': total_size_mb,
            'target_size_mb': target_size_mb,
            'estimated_parts': len(ranges),
            'pages_per_part': self.estimate_pages_per_part(target_size_mb),
            'ranges': ranges
        }


def validate_pdf_file(file_path: str) -> Tuple[bool, str]:
    """
    Validate if a file is a valid PDF.
    
    Args:
        file_path: Path to the file to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not file_path:
        return False, "No file selected"
    
    if not os.path.exists(file_path):
        return False, "File does not exist"
    
    if not file_path.lower().endswith('.pdf'):
        return False, "File is not a PDF"
    
    try:
        reader = PdfReader(file_path)
        page_count = len(reader.pages)
        
        if page_count == 0:
            return False, "PDF file has no pages"
        
        return True, ""
    except Exception as e:
        return False, f"Invalid PDF file: {str(e)}"


def validate_target_size(size_str: str, max_size_mb: float = None) -> Tuple[bool, str, float]:
    """
    Validate the target size input.
    
    Args:
        size_str: Size string to validate
        max_size_mb: Optional maximum allowed size
        
    Returns:
        Tuple of (is_valid, error_message, size_value)
    """
    try:
        size = float(size_str)
        
        if size <= 0:
            return False, "Size must be greater than 0", 0.0
        
        if size < 0.1:
            return False, "Size must be at least 0.1 MB", 0.0
        
        if max_size_mb and size > max_size_mb:
            return False, f"Size cannot exceed {max_size_mb} MB", 0.0
        
        return True, "", size
    except ValueError:
        return False, "Please enter a valid number", 0.0

