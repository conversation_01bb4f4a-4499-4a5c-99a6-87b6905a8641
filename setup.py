"""
Setup script for PDF Splitter application.
Handles installation of dependencies and initial setup.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Error: Python 3.8 or higher is required.")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ Dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False
    except FileNotFoundError:
        print("❌ Error: requirements.txt not found!")
        return False


def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = [
        "logs",
        "temp",
        "output"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"  ✅ Created: {directory}/")
        except Exception as e:
            print(f"  ❌ Error creating {directory}/: {e}")


def check_system_requirements():
    """Check system requirements and compatibility."""
    print("\n🖥️ Checking system requirements...")
    
    system = platform.system()
    print(f"  Operating System: {system}")
    
    if system == "Windows":
        print("  ✅ Windows detected - customTkinter fully supported")
    elif system == "Darwin":  # macOS
        print("  ✅ macOS detected - customTkinter supported")
    elif system == "Linux":
        print("  ✅ Linux detected - customTkinter supported")
        print("  ℹ️ Note: Make sure tkinter is installed (python3-tk package)")
    else:
        print(f"  ⚠️ Unknown system: {system} - may have compatibility issues")
    
    # Check available memory
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        print(f"  Available RAM: {memory_gb:.1f} GB")
        
        if memory_gb < 2:
            print("  ⚠️ Warning: Low memory detected. Large PDFs may cause issues.")
        else:
            print("  ✅ Sufficient memory available")
            
    except ImportError:
        print("  ℹ️ psutil not available - cannot check memory")


def test_installation():
    """Test if the installation was successful."""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        import customtkinter
        print("  ✅ customtkinter imported successfully")
        
        import PyPDF2
        print("  ✅ PyPDF2 imported successfully")
        
        from PIL import Image
        print("  ✅ Pillow imported successfully")
        
        # Test basic functionality
        from pdf_utils import validate_pdf_file, validate_target_size
        print("  ✅ PDF utilities imported successfully")
        
        from error_handler import ErrorReporter
        print("  ✅ Error handler imported successfully")
        
        # Test validation functions
        is_valid, msg, size = validate_target_size("5.0")
        assert is_valid and size == 5.0, "Target size validation failed"
        print("  ✅ Validation functions working")
        
        print("\n🎉 Installation test passed!")
        return True
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Test error: {e}")
        return False


def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)."""
    if platform.system() != "Windows":
        return
    
    print("\n🔗 Creating desktop shortcut...")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "PDF Splitter.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("  ✅ Desktop shortcut created")
        
    except ImportError:
        print("  ℹ️ winshell not available - skipping shortcut creation")
    except Exception as e:
        print(f"  ⚠️ Could not create shortcut: {e}")


def show_usage_instructions():
    """Show usage instructions."""
    print("\n📖 Usage Instructions:")
    print("=" * 50)
    print("To run the PDF Splitter application:")
    print(f"  python main.py")
    print()
    print("To run tests:")
    print(f"  python test_app.py")
    print()
    print("Features:")
    print("  • Split large PDF files into smaller parts")
    print("  • Specify target size for each part")
    print("  • Modern GUI with progress tracking")
    print("  • Comprehensive error handling")
    print("  • Cross-platform compatibility")
    print()
    print("For help and documentation, see README.md")
    print("=" * 50)


def main():
    """Main setup function."""
    print("🚀 PDF Splitter Setup")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check system requirements
    check_system_requirements()
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation.")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Test installation
    if not test_installation():
        print("\n❌ Setup failed during installation test.")
        sys.exit(1)
    
    # Create desktop shortcut (Windows only)
    create_desktop_shortcut()
    
    # Show usage instructions
    show_usage_instructions()
    
    print("\n🎉 Setup completed successfully!")
    print("You can now run the PDF Splitter application.")


if __name__ == "__main__":
    main()
