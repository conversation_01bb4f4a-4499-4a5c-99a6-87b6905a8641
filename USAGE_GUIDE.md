# PDF Splitter - <PERSON><PERSON><PERSON> de Uso

## 🚀 Como Executar a Aplicação

### Método 1: Execução Direta
```bash
python main.py
```

### Método 2: Usando o Script de Setup
```bash
python setup.py
```

## 📖 Como Usar a Aplicação

### 1. Seleção do Arquivo PDF
- Clique no botão **"Browse"** na seção "Select PDF File"
- Navegue até o arquivo PDF que deseja dividir
- Selecione o arquivo (deve ter extensão .pdf)

### 2. Definir Tamanho Alvo
- No campo "Target Size per Part (MB)", digite o tamanho desejado para cada parte
- Exemplo: `6` para criar partes de aproximadamente 6 MB cada
- Valores aceitos: números decimais maiores que 0.1 MB

### 3. Escolher Pasta de Destino
- Clique no botão **"Browse"** na seção "Output Directory"
- Selecione a pasta onde os arquivos divididos serão salvos
- <PERSON>r padrão, usa a mesma pasta do arquivo original

### 4. Executar a Divisão
- Clique no botão **"Split PDF"**
- Acompanhe o progresso na barra de progresso
- Veja as mensagens de status na área de texto

## 📁 Arquivos de Saída

Os arquivos divididos seguem o padrão de nomenclatura:
```
nome_original_part1.pdf
nome_original_part2.pdf
nome_original_part3.pdf
...
```

## ⚠️ Considerações Importantes

### Tamanho dos Arquivos
- O tamanho real de cada parte pode variar ligeiramente do tamanho alvo
- PDFs com muitas imagens resultam em partes maiores
- PDFs com muito texto resultam em partes menores
- A aplicação calcula uma estimativa baseada no conteúdo médio

### Requisitos do Sistema
- **Python**: 3.8 ou superior
- **Sistema Operacional**: Windows, macOS ou Linux
- **Memória RAM**: Mínimo 2 GB recomendado
- **Espaço em Disco**: Pelo menos 2x o tamanho do PDF original

### Limitações
- PDFs criptografados não são suportados
- PDFs corrompidos não podem ser processados
- Arquivos muito grandes podem consumir mais memória

## 🛠️ Solução de Problemas

### Erro: "PDF file is encrypted"
**Solução**: Remova a criptografia do PDF usando outro software antes de dividir.

### Erro: "Permission denied"
**Solução**: 
- Verifique se você tem permissão de leitura no arquivo PDF
- Verifique se você tem permissão de escrita na pasta de destino
- Execute como administrador se necessário

### Erro: "Insufficient disk space"
**Solução**: 
- Libere espaço em disco
- Escolha uma pasta de destino com mais espaço disponível

### Erro: "Invalid PDF file"
**Solução**:
- Verifique se o arquivo não está corrompido
- Tente abrir o PDF em outro visualizador
- Use um arquivo PDF diferente para testar

### A aplicação não inicia
**Solução**:
1. Verifique se o Python 3.8+ está instalado
2. Instale as dependências: `pip install -r requirements.txt`
3. Execute o teste: `python quick_test.py`

## 🧪 Testando a Aplicação

### Teste Rápido
```bash
python quick_test.py
```

### Teste Completo (com criação de PDFs de teste)
```bash
python test_app.py
```

## 📊 Exemplos de Uso

### Exemplo 1: PDF de 50 MB → Partes de 10 MB
- **Entrada**: documento.pdf (50 MB, 100 páginas)
- **Tamanho alvo**: 10 MB
- **Resultado esperado**: 5 arquivos de ~10 MB cada
- **Arquivos criados**:
  - documento_part1.pdf (~10 MB, páginas 1-20)
  - documento_part2.pdf (~10 MB, páginas 21-40)
  - documento_part3.pdf (~10 MB, páginas 41-60)
  - documento_part4.pdf (~10 MB, páginas 61-80)
  - documento_part5.pdf (~10 MB, páginas 81-100)

### Exemplo 2: PDF pequeno
- **Entrada**: manual.pdf (3 MB, 20 páginas)
- **Tamanho alvo**: 5 MB
- **Resultado**: 1 arquivo (não precisa dividir)
- **Arquivo criado**: manual_part1.pdf (3 MB, todas as páginas)

## 🔧 Configurações Avançadas

### Logs
- Os logs são salvos em `pdf_splitter.log`
- Contêm informações detalhadas sobre operações e erros
- Úteis para diagnóstico de problemas

### Personalização
- Modifique `main.py` para alterar a interface
- Ajuste `pdf_utils.py` para modificar a lógica de divisão
- Configure `error_handler.py` para personalizar tratamento de erros

## 📞 Suporte

Se encontrar problemas:

1. **Verifique os logs**: Consulte `pdf_splitter.log` para detalhes do erro
2. **Execute os testes**: Use `python quick_test.py` para verificar a instalação
3. **Verifique os requisitos**: Confirme que todas as dependências estão instaladas
4. **Teste com PDF simples**: Use um PDF pequeno e sem criptografia primeiro

## 🎯 Dicas de Performance

### Para PDFs Grandes (>100 MB)
- Use tamanhos alvo maiores (10-20 MB) para melhor performance
- Feche outros programas para liberar memória
- Use um SSD para melhor velocidade de escrita

### Para Muitos PDFs
- Processe um arquivo por vez
- Reinicie a aplicação periodicamente para liberar memória
- Monitore o espaço em disco disponível

## 🔄 Atualizações

Para atualizar as dependências:
```bash
pip install --upgrade -r requirements.txt
```

Para verificar a versão atual:
```bash
python -c "import customtkinter; print(customtkinter.__version__)"
```
