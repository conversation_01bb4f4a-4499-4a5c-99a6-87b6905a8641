"""
Test script for the PDF Splitter application.
Creates test PDFs and validates the splitting functionality.
"""

import os
import tempfile
import shutil
from pathlib import Path
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from pdf_utils import PDFSplitter, validate_pdf_file, validate_target_size
from error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def create_test_pdf(filename: str, num_pages: int = 10, content_type: str = "text") -> str:
    """
    Create a test PDF file with specified number of pages.
    
    Args:
        filename: Name of the PDF file to create
        num_pages: Number of pages to create
        content_type: Type of content ("text", "mixed")
        
    Returns:
        Path to the created PDF file
    """
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    for page_num in range(1, num_pages + 1):
        # Add title
        c.setFont("Helvetica-Bold", 16)
        c.drawString(50, height - 50, f"Test PDF - Page {page_num}")
        
        # Add content based on type
        if content_type == "text":
            c.set<PERSON><PERSON>("Helvetica", 12)
            y_position = height - 100
            
            # Add multiple lines of text
            for line_num in range(1, 30):
                text = f"This is line {line_num} on page {page_num}. " * 3
                c.drawString(50, y_position, text[:80])  # Limit line length
                y_position -= 20
                
                if y_position < 50:  # Don't go too close to bottom
                    break
        
        elif content_type == "mixed":
            c.setFont("Helvetica", 12)
            y_position = height - 100
            
            # Add some text
            for line_num in range(1, 15):
                text = f"Mixed content page {page_num}, line {line_num}. "
                c.drawString(50, y_position, text)
                y_position -= 15
            
            # Add a simple rectangle (simulating an image)
            c.setFillColorRGB(0.8, 0.8, 0.8)
            c.rect(50, y_position - 100, 200, 80, fill=1)
            c.setFillColorRGB(0, 0, 0)
            c.drawString(60, y_position - 60, f"Simulated image on page {page_num}")
        
        c.showPage()
    
    c.save()
    return filename


def test_pdf_creation():
    """Test PDF creation functionality."""
    print("🧪 Testing PDF creation...")
    
    test_dir = "test_output"
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        # Create test PDFs of different sizes
        small_pdf = create_test_pdf(os.path.join(test_dir, "small_test.pdf"), 5, "text")
        medium_pdf = create_test_pdf(os.path.join(test_dir, "medium_test.pdf"), 20, "mixed")
        large_pdf = create_test_pdf(os.path.join(test_dir, "large_test.pdf"), 50, "mixed")
        
        # Check file sizes
        for pdf_path in [small_pdf, medium_pdf, large_pdf]:
            size_mb = os.path.getsize(pdf_path) / (1024 * 1024)
            print(f"  ✅ Created {os.path.basename(pdf_path)}: {size_mb:.2f} MB")
        
        return [small_pdf, medium_pdf, large_pdf]
        
    except Exception as e:
        print(f"  ❌ Error creating test PDFs: {str(e)}")
        return []


def test_pdf_validation():
    """Test PDF validation functions."""
    print("\n🧪 Testing PDF validation...")
    
    # Test with non-existent file
    is_valid, msg = validate_pdf_file("nonexistent.pdf")
    assert not is_valid, "Should fail for non-existent file"
    print("  ✅ Non-existent file validation: PASS")
    
    # Test with non-PDF file
    test_file = "test_output/test.txt"
    with open(test_file, 'w') as f:
        f.write("This is not a PDF")
    
    is_valid, msg = validate_pdf_file(test_file)
    assert not is_valid, "Should fail for non-PDF file"
    print("  ✅ Non-PDF file validation: PASS")
    
    # Test target size validation
    test_cases = [
        ("5", True),
        ("0.5", True),
        ("0", False),
        ("-1", False),
        ("abc", False),
        ("", False)
    ]
    
    for size_str, should_be_valid in test_cases:
        is_valid, msg, size = validate_target_size(size_str)
        assert is_valid == should_be_valid, f"Size validation failed for '{size_str}'"
    
    print("  ✅ Target size validation: PASS")


def test_pdf_splitting(test_pdfs):
    """Test PDF splitting functionality."""
    print("\n🧪 Testing PDF splitting...")
    
    if not test_pdfs:
        print("  ⚠️ No test PDFs available for splitting test")
        return
    
    test_pdf = test_pdfs[1]  # Use medium PDF
    output_dir = "test_output/split_results"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Test splitting
        splitter = PDFSplitter(test_pdf)
        
        # Get info
        info = splitter.get_split_info(2.0)  # 2 MB target size
        print(f"  📊 PDF Info: {info['total_pages']} pages, {info['total_size_mb']:.2f} MB")
        print(f"  📦 Estimated parts: {info['estimated_parts']}")
        
        # Split the PDF
        def progress_callback(current, total, message):
            print(f"    Progress: {current}/{total} - {message}")
        
        created_files = splitter.split_pdf(output_dir, 2.0, progress_callback)
        
        print(f"  ✅ Successfully created {len(created_files)} parts")
        
        # Verify created files
        total_size = 0
        for file_path in created_files:
            if os.path.exists(file_path):
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                total_size += size_mb
                print(f"    📄 {os.path.basename(file_path)}: {size_mb:.2f} MB")
            else:
                print(f"    ❌ Missing file: {os.path.basename(file_path)}")
        
        print(f"  📊 Total output size: {total_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error during splitting: {str(e)}")
        return False


def test_error_handling():
    """Test error handling scenarios."""
    print("\n🧪 Testing error handling...")
    
    error_reporter = ErrorReporter()
    
    # Test error reporter
    error_reporter.add_error("Test error message")
    error_reporter.add_warning("Test warning message")
    
    assert error_reporter.has_errors(), "Should have errors"
    assert error_reporter.has_warnings(), "Should have warnings"
    
    summary = error_reporter.get_summary()
    assert "Test error message" in summary, "Summary should contain error"
    assert "Test warning message" in summary, "Summary should contain warning"
    
    print("  ✅ Error reporter: PASS")
    
    # Test invalid PDF handling
    try:
        splitter = PDFSplitter("nonexistent.pdf")
        print("  ❌ Should have raised exception for non-existent file")
    except FileNotFoundError:
        print("  ✅ Non-existent file handling: PASS")
    except Exception as e:
        print(f"  ❌ Unexpected exception: {str(e)}")


def cleanup_test_files():
    """Clean up test files and directories."""
    print("\n🧹 Cleaning up test files...")
    
    try:
        if os.path.exists("test_output"):
            shutil.rmtree("test_output")
        print("  ✅ Test files cleaned up")
    except Exception as e:
        print(f"  ⚠️ Error cleaning up: {str(e)}")


def run_all_tests():
    """Run all tests."""
    print("🚀 Starting PDF Splitter Tests\n")
    
    try:
        # Check if reportlab is available for test PDF creation
        try:
            import reportlab
        except ImportError:
            print("⚠️ reportlab not installed. Installing for testing...")
            os.system("pip install reportlab")
            import reportlab
        
        # Run tests
        test_pdfs = test_pdf_creation()
        test_pdf_validation()
        splitting_success = test_pdf_splitting(test_pdfs)
        test_error_handling()
        
        print("\n📊 Test Summary:")
        print("  ✅ PDF Creation: PASS")
        print("  ✅ PDF Validation: PASS")
        print(f"  {'✅' if splitting_success else '❌'} PDF Splitting: {'PASS' if splitting_success else 'FAIL'}")
        print("  ✅ Error Handling: PASS")
        
        if splitting_success:
            print("\n🎉 All tests passed! The PDF Splitter is ready to use.")
        else:
            print("\n⚠️ Some tests failed. Please check the implementation.")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
    
    finally:
        cleanup_test_files()


if __name__ == "__main__":
    run_all_tests()
