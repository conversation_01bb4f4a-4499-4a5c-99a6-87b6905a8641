"""
Error handling utilities for the PDF Splitter application.
"""

import os
import logging
import traceback
from typing import Tuple, Optional
from functools import wraps


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_splitter.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class PDFSplitterError(Exception):
    """Base exception for PDF Splitter application."""
    pass


class FileValidationError(PDFSplitterError):
    """Exception raised for file validation errors."""
    pass


class SizeValidationError(PDFSplitterError):
    """Exception raised for size validation errors."""
    pass


class SplittingError(PDFSplitterError):
    """Exception raised during PDF splitting operations."""
    pass


class DiskSpaceError(PDFSplitterError):
    """Exception raised when there's insufficient disk space."""
    pass


def handle_exceptions(func):
    """Decorator to handle exceptions in GUI methods."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in {func.__name__}: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    return wrapper


def check_disk_space(directory: str, required_mb: float) -> Tuple[bool, str]:
    """
    Check if there's enough disk space for the operation.
    
    Args:
        directory: Directory to check
        required_mb: Required space in MB
        
    Returns:
        Tuple of (has_space, error_message)
    """
    try:
        if not os.path.exists(directory):
            return False, "Directory does not exist"
        
        # Get free space
        if os.name == 'nt':  # Windows
            import shutil
            free_bytes = shutil.disk_usage(directory).free
        else:  # Unix/Linux/Mac
            statvfs = os.statvfs(directory)
            free_bytes = statvfs.f_frsize * statvfs.f_bavail
        
        free_mb = free_bytes / (1024 * 1024)
        
        # Add 10% buffer for safety
        required_with_buffer = required_mb * 1.1
        
        if free_mb < required_with_buffer:
            return False, (f"Insufficient disk space. Required: {required_with_buffer:.1f} MB, "
                          f"Available: {free_mb:.1f} MB")
        
        return True, ""
        
    except Exception as e:
        return False, f"Error checking disk space: {str(e)}"


def validate_file_permissions(file_path: str, output_dir: str) -> Tuple[bool, str]:
    """
    Validate file permissions for reading input and writing output.
    
    Args:
        file_path: Path to input PDF file
        output_dir: Output directory path
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Check read permission for input file
        if not os.access(file_path, os.R_OK):
            return False, "No read permission for the selected PDF file"
        
        # Check write permission for output directory
        if not os.access(output_dir, os.W_OK):
            return False, "No write permission for the output directory"
        
        return True, ""
        
    except Exception as e:
        return False, f"Error checking file permissions: {str(e)}"


def validate_pdf_integrity(file_path: str) -> Tuple[bool, str]:
    """
    Perform additional PDF integrity checks.
    
    Args:
        file_path: Path to PDF file
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        from PyPDF2 import PdfReader
        
        reader = PdfReader(file_path)
        
        # Check if PDF is encrypted
        if reader.is_encrypted:
            return False, "PDF file is encrypted and cannot be split"
        
        # Try to read first and last page to ensure integrity
        total_pages = len(reader.pages)
        
        if total_pages == 0:
            return False, "PDF file contains no pages"
        
        # Test reading first page
        try:
            _ = reader.pages[0]
        except Exception:
            return False, "Cannot read first page of PDF"
        
        # Test reading last page
        try:
            _ = reader.pages[total_pages - 1]
        except Exception:
            return False, "Cannot read last page of PDF"
        
        return True, ""
        
    except Exception as e:
        return False, f"PDF integrity check failed: {str(e)}"


def estimate_output_size(input_file_path: str, target_size_mb: float) -> Tuple[float, int]:
    """
    Estimate total output size and number of files.
    
    Args:
        input_file_path: Path to input PDF
        target_size_mb: Target size per part
        
    Returns:
        Tuple of (estimated_total_size_mb, estimated_file_count)
    """
    try:
        input_size_mb = os.path.getsize(input_file_path) / (1024 * 1024)
        
        # PDF splitting typically adds 5-10% overhead due to file structure duplication
        overhead_factor = 1.08
        estimated_total_size = input_size_mb * overhead_factor
        
        estimated_file_count = max(1, int(estimated_total_size / target_size_mb))
        
        return estimated_total_size, estimated_file_count
        
    except Exception as e:
        logger.error(f"Error estimating output size: {str(e)}")
        return 0.0, 0


def safe_file_operation(operation_func, *args, **kwargs):
    """
    Safely execute a file operation with proper error handling.
    
    Args:
        operation_func: Function to execute
        *args: Arguments for the function
        **kwargs: Keyword arguments for the function
        
    Returns:
        Result of the operation
        
    Raises:
        Appropriate exception based on the error type
    """
    try:
        return operation_func(*args, **kwargs)
    except PermissionError as e:
        raise FileValidationError(f"Permission denied: {str(e)}")
    except FileNotFoundError as e:
        raise FileValidationError(f"File not found: {str(e)}")
    except OSError as e:
        if "No space left on device" in str(e).lower():
            raise DiskSpaceError("Insufficient disk space")
        else:
            raise SplittingError(f"File system error: {str(e)}")
    except Exception as e:
        raise SplittingError(f"Unexpected error: {str(e)}")


def log_operation_start(operation: str, **kwargs):
    """Log the start of an operation."""
    params = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
    logger.info(f"Starting {operation}: {params}")


def log_operation_success(operation: str, **kwargs):
    """Log successful completion of an operation."""
    params = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
    logger.info(f"Successfully completed {operation}: {params}")


def log_operation_error(operation: str, error: Exception, **kwargs):
    """Log an operation error."""
    params = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
    logger.error(f"Error in {operation}: {str(error)}: {params}")


def get_user_friendly_error_message(error: Exception) -> str:
    """
    Convert technical errors to user-friendly messages.
    
    Args:
        error: Exception object
        
    Returns:
        User-friendly error message
    """
    error_str = str(error).lower()
    
    if isinstance(error, FileValidationError):
        return str(error)
    elif isinstance(error, SizeValidationError):
        return str(error)
    elif isinstance(error, DiskSpaceError):
        return str(error)
    elif "permission" in error_str or "access" in error_str:
        return "Permission denied. Please check file and folder permissions."
    elif "no space" in error_str or "disk full" in error_str:
        return "Insufficient disk space. Please free up space and try again."
    elif "corrupted" in error_str or "invalid" in error_str:
        return "The PDF file appears to be corrupted or invalid."
    elif "encrypted" in error_str:
        return "The PDF file is encrypted and cannot be split."
    elif "memory" in error_str:
        return "Insufficient memory. Try splitting into larger parts or close other applications."
    elif "network" in error_str:
        return "Network error. Please check your connection if the file is on a network drive."
    else:
        return f"An unexpected error occurred: {str(error)}"


class ErrorReporter:
    """Class for collecting and reporting errors during operations."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def add_error(self, message: str, exception: Optional[Exception] = None):
        """Add an error to the report."""
        self.errors.append({
            'message': message,
            'exception': exception,
            'type': 'error'
        })
        if exception:
            logger.error(f"{message}: {str(exception)}")
        else:
            logger.error(message)
    
    def add_warning(self, message: str):
        """Add a warning to the report."""
        self.warnings.append({
            'message': message,
            'type': 'warning'
        })
        logger.warning(message)
    
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0
    
    def get_summary(self) -> str:
        """Get a summary of all errors and warnings."""
        summary = []
        
        if self.errors:
            summary.append(f"Errors ({len(self.errors)}):")
            for error in self.errors:
                summary.append(f"  • {error['message']}")
        
        if self.warnings:
            summary.append(f"Warnings ({len(self.warnings)}):")
            for warning in self.warnings:
                summary.append(f"  • {warning['message']}")
        
        return "\n".join(summary) if summary else "No errors or warnings."
    
    def clear(self):
        """Clear all errors and warnings."""
        self.errors.clear()
        self.warnings.clear()
