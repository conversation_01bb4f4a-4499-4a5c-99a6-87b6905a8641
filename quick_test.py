"""
Quick test to verify the PDF Splitter components work correctly.
"""

import os
import sys

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import customtkinter as ctk
        print("  ✅ customtkinter imported successfully")
        
        import PyPDF2
        print("  ✅ PyPDF2 imported successfully")
        
        from PIL import Image
        print("  ✅ Pillow imported successfully")
        
        from pdf_utils import PDFSplitter, validate_pdf_file, validate_target_size
        print("  ✅ pdf_utils imported successfully")
        
        from error_handler import ErrorReporter
        print("  ✅ error_handler imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False


def test_validation_functions():
    """Test validation functions."""
    print("\n🧪 Testing validation functions...")
    
    try:
        from pdf_utils import validate_target_size
        
        # Test valid sizes
        valid_cases = ["1", "5.5", "10", "0.5"]
        for case in valid_cases:
            is_valid, msg, size = validate_target_size(case)
            if not is_valid:
                print(f"  ❌ Failed for valid case: {case}")
                return False
        
        # Test invalid sizes
        invalid_cases = ["0", "-1", "abc", ""]
        for case in invalid_cases:
            is_valid, msg, size = validate_target_size(case)
            if is_valid:
                print(f"  ❌ Should have failed for invalid case: {case}")
                return False
        
        print("  ✅ Validation functions working correctly")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing validation: {e}")
        return False


def test_error_handler():
    """Test error handling functionality."""
    print("\n🧪 Testing error handler...")
    
    try:
        from error_handler import ErrorReporter
        
        reporter = ErrorReporter()
        reporter.add_error("Test error")
        reporter.add_warning("Test warning")
        
        if not reporter.has_errors():
            print("  ❌ Error reporter should have errors")
            return False
        
        if not reporter.has_warnings():
            print("  ❌ Error reporter should have warnings")
            return False
        
        summary = reporter.get_summary()
        if "Test error" not in summary or "Test warning" not in summary:
            print("  ❌ Summary doesn't contain expected messages")
            return False
        
        print("  ✅ Error handler working correctly")
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing error handler: {e}")
        return False


def test_gui_creation():
    """Test if GUI can be created without errors."""
    print("\n🧪 Testing GUI creation...")
    
    try:
        import customtkinter as ctk
        
        # Set appearance mode
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        # Create a test window
        root = ctk.CTk()
        root.title("Test Window")
        root.geometry("300x200")
        
        # Create a test widget
        label = ctk.CTkLabel(root, text="Test Label")
        label.pack(pady=20)
        
        button = ctk.CTkButton(root, text="Test Button")
        button.pack(pady=10)
        
        # Destroy the window immediately
        root.destroy()
        
        print("  ✅ GUI components created successfully")
        return True
        
    except Exception as e:
        print(f"  ❌ Error creating GUI: {e}")
        return False


def main():
    """Run all quick tests."""
    print("🚀 PDF Splitter Quick Test")
    print("=" * 30)
    
    tests = [
        ("Import Test", test_imports),
        ("Validation Test", test_validation_functions),
        ("Error Handler Test", test_error_handler),
        ("GUI Creation Test", test_gui_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The PDF Splitter is ready to use.")
        print("\nTo run the application:")
        print("  python main.py")
    else:
        print("⚠️ Some tests failed. Please check the installation.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
