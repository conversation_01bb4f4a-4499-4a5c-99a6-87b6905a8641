"""
PDF Splitter Application
A modern desktop application for splitting large PDF files into smaller parts.
"""

import os
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from pdf_utils import PDFSplitter, validate_pdf_file, validate_target_size
from error_handler import (
    handle_exceptions, check_disk_space, validate_file_permissions,
    validate_pdf_integrity, estimate_output_size, safe_file_operation,
    log_operation_start, log_operation_success, log_operation_error,
    get_user_friendly_error_message, ErrorReporter,
    FileValidationError, SizeValidationError, SplittingError, DiskSpaceError
)


class PDFSplitterApp:
    """Main application class for the PDF Splitter."""
    
    def __init__(self):
        """Initialize the application."""
        # Set appearance mode and color theme
        ctk.set_appearance_mode("system")  # Modes: system (default), light, dark
        ctk.set_default_color_theme("blue")  # Themes: blue (default), dark-blue, green
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("PDF Splitter")
        self.root.geometry("700x600")
        self.root.minsize(600, 500)
        
        # Variables
        self.selected_file = tk.StringVar()
        self.target_size = tk.StringVar(value="6")
        self.output_dir = tk.StringVar()
        self.is_splitting = False
        
        # Create GUI
        self.create_widgets()
        
        # Center window on screen
        self.center_window()
    
    def center_window(self):
        """Center the window on the screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Create and arrange all GUI widgets."""
        # Main container
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="PDF Splitter",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # File selection section
        self.create_file_selection_section(main_frame)
        
        # Target size section
        self.create_target_size_section(main_frame)
        
        # Output directory section
        self.create_output_dir_section(main_frame)
        
        # Split button
        self.split_button = ctk.CTkButton(
            main_frame,
            text="Split PDF",
            command=self.start_split,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.split_button.pack(pady=(30, 20))
        
        # Progress section
        self.create_progress_section(main_frame)
    
    def create_file_selection_section(self, parent):
        """Create the file selection section."""
        # File selection frame
        file_frame = ctk.CTkFrame(parent)
        file_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Label
        file_label = ctk.CTkLabel(
            file_frame,
            text="Select PDF File:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        file_label.pack(anchor="w", padx=20, pady=(20, 10))
        
        # File path display and browse button container
        file_input_frame = ctk.CTkFrame(file_frame)
        file_input_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # File path display
        self.file_entry = ctk.CTkEntry(
            file_input_frame,
            textvariable=self.selected_file,
            placeholder_text="No file selected...",
            state="readonly",
            height=35
        )
        self.file_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        # Browse button
        browse_button = ctk.CTkButton(
            file_input_frame,
            text="Browse",
            command=self.browse_file,
            width=100,
            height=35
        )
        browse_button.pack(side="right", padx=(5, 10), pady=10)
    
    def create_target_size_section(self, parent):
        """Create the target size input section."""
        # Target size frame
        size_frame = ctk.CTkFrame(parent)
        size_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Label
        size_label = ctk.CTkLabel(
            size_frame,
            text="Target Size per Part (MB):",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        size_label.pack(anchor="w", padx=20, pady=(20, 10))
        
        # Size input
        self.size_entry = ctk.CTkEntry(
            size_frame,
            textvariable=self.target_size,
            placeholder_text="Enter size in MB (e.g., 6)",
            height=35,
            width=200
        )
        self.size_entry.pack(anchor="w", padx=20, pady=(0, 20))
    
    def create_output_dir_section(self, parent):
        """Create the output directory selection section."""
        # Output directory frame
        output_frame = ctk.CTkFrame(parent)
        output_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Label
        output_label = ctk.CTkLabel(
            output_frame,
            text="Output Directory:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        output_label.pack(anchor="w", padx=20, pady=(20, 10))
        
        # Output path display and browse button container
        output_input_frame = ctk.CTkFrame(output_frame)
        output_input_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Output path display
        self.output_entry = ctk.CTkEntry(
            output_input_frame,
            textvariable=self.output_dir,
            placeholder_text="Choose output directory...",
            state="readonly",
            height=35
        )
        self.output_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        # Browse button
        output_browse_button = ctk.CTkButton(
            output_input_frame,
            text="Browse",
            command=self.browse_output_dir,
            width=100,
            height=35
        )
        output_browse_button.pack(side="right", padx=(5, 10), pady=10)
    
    def create_progress_section(self, parent):
        """Create the progress and status section."""
        # Progress frame
        progress_frame = ctk.CTkFrame(parent)
        progress_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Progress label
        progress_label = ctk.CTkLabel(
            progress_frame,
            text="Status:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        progress_label.pack(anchor="w", padx=20, pady=(20, 10))
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(progress_frame)
        self.progress_bar.pack(fill="x", padx=20, pady=(0, 10))
        self.progress_bar.set(0)
        
        # Status text area
        self.status_text = ctk.CTkTextbox(
            progress_frame,
            height=120,
            wrap="word"
        )
        self.status_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Initial status message
        self.update_status("Ready to split PDF files.")
    
    def browse_file(self):
        """Open file dialog to select PDF file."""
        file_path = filedialog.askopenfilename(
            title="Select PDF File",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        
        if file_path:
            self.selected_file.set(file_path)
            self.update_status(f"Selected file: {os.path.basename(file_path)}")
            
            # Auto-set output directory to same as input file
            if not self.output_dir.get():
                input_dir = os.path.dirname(file_path)
                self.output_dir.set(input_dir)
    
    def browse_output_dir(self):
        """Open directory dialog to select output directory."""
        dir_path = filedialog.askdirectory(title="Select Output Directory")
        
        if dir_path:
            self.output_dir.set(dir_path)
            self.update_status(f"Output directory: {dir_path}")
    
    def update_status(self, message):
        """Update the status text area."""
        self.status_text.insert("end", f"{message}\n")
        self.status_text.see("end")
        self.root.update_idletasks()
    
    def update_progress(self, current, total, message):
        """Update progress bar and status."""
        if total > 0:
            progress = current / total
            self.progress_bar.set(progress)
        
        self.update_status(message)
    
    @handle_exceptions
    def validate_inputs(self):
        """Validate all user inputs with comprehensive error checking."""
        error_reporter = ErrorReporter()

        # Validate PDF file
        pdf_path = self.selected_file.get()
        is_valid, error_msg = validate_pdf_file(pdf_path)
        if not is_valid:
            error_reporter.add_error(f"PDF file validation failed: {error_msg}")
        else:
            # Additional PDF integrity checks
            is_valid, error_msg = validate_pdf_integrity(pdf_path)
            if not is_valid:
                error_reporter.add_error(f"PDF integrity check failed: {error_msg}")

        # Validate target size
        is_valid, error_msg, size_value = validate_target_size(self.target_size.get())
        if not is_valid:
            error_reporter.add_error(f"Target size validation failed: {error_msg}")

        # Validate output directory
        output_dir = self.output_dir.get()
        if not output_dir:
            error_reporter.add_error("No output directory selected")
        else:
            # Create directory if it doesn't exist
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                except Exception as e:
                    error_reporter.add_error(f"Cannot create output directory: {str(e)}")

            # Check file permissions
            if pdf_path and os.path.exists(pdf_path):
                is_valid, error_msg = validate_file_permissions(pdf_path, output_dir)
                if not is_valid:
                    error_reporter.add_error(f"Permission check failed: {error_msg}")

            # Check disk space
            if pdf_path and os.path.exists(pdf_path) and size_value > 0:
                estimated_size, file_count = estimate_output_size(pdf_path, size_value)
                has_space, space_msg = check_disk_space(output_dir, estimated_size)
                if not has_space:
                    error_reporter.add_error(f"Disk space check failed: {space_msg}")
                else:
                    self.update_status(f"Estimated output: {file_count} files, ~{estimated_size:.1f} MB total")

        # Show errors if any
        if error_reporter.has_errors():
            messagebox.showerror("Validation Failed", error_reporter.get_summary())
            return False

        # Show warnings if any
        if error_reporter.has_warnings():
            result = messagebox.askyesno(
                "Warnings Found",
                f"{error_reporter.get_summary()}\n\nDo you want to continue anyway?"
            )
            if not result:
                return False

        return True
    
    def start_split(self):
        """Start the PDF splitting process."""
        if self.is_splitting:
            return
        
        if not self.validate_inputs():
            return
        
        # Disable the split button
        self.is_splitting = True
        self.split_button.configure(state="disabled", text="Splitting...")
        
        # Clear previous status
        self.status_text.delete("1.0", "end")
        self.progress_bar.set(0)
        
        # Start splitting in a separate thread
        thread = threading.Thread(target=self.split_pdf_thread)
        thread.daemon = True
        thread.start()
    
    def split_pdf_thread(self):
        """PDF splitting thread function with enhanced error handling."""
        error_reporter = ErrorReporter()

        try:
            # Get values
            pdf_path = self.selected_file.get()
            target_size = float(self.target_size.get())
            output_dir = self.output_dir.get()

            log_operation_start("PDF Split",
                              pdf_path=pdf_path,
                              target_size=target_size,
                              output_dir=output_dir)

            # Create splitter with error handling
            splitter = safe_file_operation(PDFSplitter, pdf_path)

            # Show split info
            info = splitter.get_split_info(target_size)
            self.update_status(f"📊 PDF Analysis:")
            self.update_status(f"  📄 Total pages: {info['total_pages']}")
            self.update_status(f"  💾 File size: {info['total_size_mb']:.2f} MB")
            self.update_status(f"  📦 Estimated parts: {info['estimated_parts']}")
            self.update_status(f"  📃 Pages per part: ~{info['pages_per_part']}")
            self.update_status("")

            # Final validation before splitting
            estimated_size, file_count = estimate_output_size(pdf_path, target_size)
            has_space, space_msg = check_disk_space(output_dir, estimated_size)
            if not has_space:
                raise DiskSpaceError(space_msg)

            self.update_status(f"🚀 Starting split operation...")

            # Split the PDF with error handling
            created_files = safe_file_operation(
                splitter.split_pdf,
                output_dir,
                target_size,
                self.update_progress
            )

            # Verify created files
            total_output_size = 0
            for file_path in created_files:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / (1024 * 1024)
                    total_output_size += file_size
                else:
                    error_reporter.add_warning(f"Created file not found: {os.path.basename(file_path)}")

            # Success message
            self.update_status("")
            self.update_status(f"✅ Successfully split PDF into {len(created_files)} parts!")
            self.update_status(f"📁 Files saved to: {output_dir}")
            self.update_status(f"💾 Total output size: {total_output_size:.2f} MB")

            log_operation_success("PDF Split",
                                parts_created=len(created_files),
                                total_size_mb=total_output_size)

            # Show completion dialog
            success_msg = (f"PDF successfully split into {len(created_files)} parts!\n\n"
                          f"Total size: {total_output_size:.2f} MB\n"
                          f"Files saved to:\n{output_dir}")

            if error_reporter.has_warnings():
                success_msg += f"\n\nWarnings:\n{error_reporter.get_summary()}"

            self.root.after(0, lambda: messagebox.showinfo("Success", success_msg))

        except (FileValidationError, SizeValidationError, DiskSpaceError) as e:
            error_msg = f"❌ Validation Error: {str(e)}"
            self.update_status(error_msg)
            log_operation_error("PDF Split", e)
            self.root.after(0, lambda: messagebox.showerror("Validation Error", str(e)))

        except SplittingError as e:
            error_msg = f"❌ Splitting Error: {str(e)}"
            self.update_status(error_msg)
            log_operation_error("PDF Split", e)
            self.root.after(0, lambda: messagebox.showerror("Splitting Error", str(e)))

        except Exception as e:
            friendly_msg = get_user_friendly_error_message(e)
            error_msg = f"❌ Error: {friendly_msg}"
            self.update_status(error_msg)
            log_operation_error("PDF Split", e)
            self.root.after(0, lambda: messagebox.showerror("Error", friendly_msg))

        finally:
            # Re-enable the split button
            self.root.after(0, self.reset_split_button)
    
    def reset_split_button(self):
        """Reset the split button to its normal state."""
        self.is_splitting = False
        self.split_button.configure(state="normal", text="Split PDF")
    
    def run(self):
        """Start the application."""
        self.root.mainloop()


def main():
    """Main function to run the application."""
    try:
        app = PDFSplitterApp()
        app.run()
    except Exception as e:
        messagebox.showerror("Application Error", f"Failed to start application: {str(e)}")


if __name__ == "__main__":
    main()
